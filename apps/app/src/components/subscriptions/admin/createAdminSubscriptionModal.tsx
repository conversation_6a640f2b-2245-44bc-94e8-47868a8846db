"use client"

import React, { useState } from "react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, ModalHeader } from "@nextui-org/modal"
import { Select, SelectItem } from "@nextui-org/select"
import { BillingPeriod } from "@prisma/client"

interface CreateAdminSubscriptionModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

const CreateAdminSubscriptionModal = ({ isOpen, onClose, onSuccess }: CreateAdminSubscriptionModalProps) => {
  const [selectedUserId, setSelectedUserId] = useState<string>("")
  const [selectedPlanId, setSelectedPlanId] = useState<number>(0)
  const [billingPeriod, setBillingPeriod] = useState<BillingPeriod>(BillingPeriod.MONTHLY)
  const [startDate, setStartDate] = useState<string>(new Date().toISOString().split("T")[0])
  const [endDate, setEndDate] = useState<string>(
    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
  )
  const [userSearchQuery, setUserSearchQuery] = useState<string>("")

  const { data: users } = trpc.user.searchForAdmin.useQuery(
    { query: userSearchQuery },
    { enabled: userSearchQuery.length >= 2 }
  )

  const { data: plans } = trpc.plan.getAll.useQuery()

  const createAdminSubscription = trpc.subscription.createAdminManaged.useMutation({
    onSuccess: () => {
      toast.success("Abonnement créé avec succès")
      onSuccess()
      onClose()
      resetForm()
    },
    onError: (error) => {
      toast.error(`Erreur lors de la création: ${error.message}`)
    },
  })

  const resetForm = () => {
    setSelectedUserId("")
    setSelectedPlanId(0)
    setBillingPeriod(BillingPeriod.MONTHLY)
    setStartDate(new Date().toISOString().split("T")[0])
    setEndDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0])
    setUserSearchQuery("")
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedUserId || !selectedPlanId) {
      toast.error("Veuillez sélectionner un utilisateur et un plan")
      return
    }

    createAdminSubscription.mutate({
      userId: selectedUserId,
      planId: selectedPlanId,
      billingPeriod,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
    })
  }

  const getBillingPeriodLabel = (period: BillingPeriod) => {
    return period === "MONTHLY" ? "Mensuel" : "Annuel"
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl">
      <ModalContent>
        {(onClose) => (
          <form onSubmit={handleSubmit}>
            <ModalHeader className="flex flex-col gap-1">
              Créer un abonnement
            </ModalHeader>
            <ModalBody>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="md:col-span-2">
                  <Input
                    label="Rechercher un utilisateur"
                    placeholder="Tapez le nom ou l'email..."
                    value={userSearchQuery}
                    onChange={(e) => setUserSearchQuery(e.target.value)}
                    description="Tapez au moins 2 caractères pour rechercher"
                  />
                  {users && users.length > 0 && (
                    <Select
                      label="Sélectionner l'utilisateur"
                      selectedKeys={selectedUserId ? [selectedUserId] : []}
                      onChange={(e) => setSelectedUserId(e.target.value)}
                      className="mt-2"
                      isRequired
                    >
                      {users.map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.name} ({user.email})
                        </SelectItem>
                      ))}
                    </Select>
                  )}
                </div>

                <div>
                  {!!plans && (
                    <Select
                      label="Plan"
                      selectedKeys={selectedPlanId ? [selectedPlanId.toString()] : []}
                      onChange={(e) => setSelectedPlanId(Number(e.target.value))}
                      isRequired
                    >
                      {plans?.map((plan) => (
                        <SelectItem key={plan.id.toString()} value={plan.id.toString()}>
                          {plan.name} {!plan.isActive ? "(Inactif)" : ""}
                        </SelectItem>
                      ))}
                    </Select>
                  )}
                </div>

                <div>
                  <Select
                    label="Période de facturation"
                    selectedKeys={[billingPeriod]}
                    onChange={(e) => setBillingPeriod(e.target.value as BillingPeriod)}
                    isRequired
                  >
                    {Object.values(BillingPeriod).map((period) => (
                      <SelectItem key={period} value={period}>
                        {getBillingPeriodLabel(period)}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <div>
                  <Input
                    type="date"
                    label="Date de début"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    isRequired
                  />
                </div>

                <div>
                  <Input
                    type="date"
                    label="Date de fin"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    isRequired
                  />
                </div>
              </div>

              <div className="mt-4 rounded-lg bg-warning-50 p-4 dark:bg-warning-100/20">
                <h4 className="font-medium text-warning-700 dark:text-warning-600">
                  ⚠️ Abonnement géré par l&apos;administrateur
                </h4>
                <ul className="mt-2 text-sm text-warning-600 dark:text-warning-500">
                  <li>• Cet abonnement ne sera pas renouvelé automatiquement</li>
                  <li>• Il peut être lié à des plans inactifs</li>
                  <li>• Il expirera à la date de fin sans tentative de paiement</li>
                  <li>• Aucun enregistrement MangoPay ne sera créé</li>
                </ul>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Annuler
              </Button>
              <Button
                color="primary"
                type="submit"
                isLoading={createAdminSubscription.isPending}
                isDisabled={!selectedUserId || !selectedPlanId}
              >
                Créer l&apos;abonnement
              </Button>
            </ModalFooter>
          </form>
        )}
      </ModalContent>
    </Modal>
  )
}

export default CreateAdminSubscriptionModal
