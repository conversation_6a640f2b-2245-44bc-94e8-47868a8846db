import React, { useLayoutEffect, useMemo, useRef, useState } from "react"

import { Select, SelectItem } from "@nextui-org/select";

type Option = { label: string; key: string; name?: string }

interface SelectWithSearchProps {
    options: Option[]
    label?: string
    value: string
    onChange: (value: string) => void
    placeholder?: string
    errorMessage?: string
    isInvalid?: boolean
    radius?: "sm" | "md" | "lg"
    classNames?: {
        base?: string
        trigger?: string
        popoverContent?: string
        selectorIcon?: string
        input?: string
    }
}

const SelectWithSearch: React.FC<SelectWithSearchProps> = ({
    options,
    label,
    value,
    onChange,
    placeholder,
    errorMessage,
    isInvalid,
    classNames,
    radius = "sm",
}) => {
    const [search, setSearch] = useState("")
    const searchInputRef = useRef<HTMLInputElement>(null)

    const filtered = useMemo(() => {
        return options.filter(
            (opt) =>
                opt.label.toLowerCase().includes(search.toLowerCase()) ||
                opt.key.includes(search) ||
                (opt.name && opt.name.toLowerCase().includes(search.toLowerCase()))
        )
    }, [search, options])

    const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        onChange(e.target.value)
        setSearch("")
    }

    useLayoutEffect(() => {
        if (searchInputRef.current) {
            searchInputRef.current.focus()
        }
    }, [search])

    return (
        <Select
            label={label}
            radius={radius}
            selectedKeys={[value]}
            onChange={handleChange}
            placeholder={placeholder}
            errorMessage={errorMessage}
            isInvalid={isInvalid}
            classNames={classNames}
            listboxProps={{
                topContent: (
                    <div className="p-2" role="search">
                        <input
                            ref={searchInputRef}
                            type="text"
                            placeholder="Search..."
                            value={search}
                            onChange={(e) => setSearch(e.target.value)}
                            className="w-full rounded bg-black p-2 text-sm outline-none ring-0"
                            aria-label="Search countries"
                            tabIndex={0}
                        />
                    </div>
                ),
            }}
        >
            {filtered.map((opt) => (
                <SelectItem key={opt.key} textValue={opt.label}>
                    {opt.label}
                </SelectItem>
            ))}
        </Select>
    )
}

export default SelectWithSearch

